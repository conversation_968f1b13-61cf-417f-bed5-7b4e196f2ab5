#include "iq_stream_config_helpers.h"
#include "../node/v8_helpers.h"

namespace IQStreamConfigHelpers {

std::tuple<WAVStreamConfig, std::string> convertWAVConfigFromJS(v8::Isolate* isolate, const v8::Local<v8::Object>& jsConfig) {
  WAVStreamConfig config;
  if (!NodeHelpers::GetString(isolate, jsConfig, "path", config.path) || config.path.empty()) {
    return {config, "stream.wav.path is required"};
  }
  if (!NodeHelpers::GetBool(isolate, jsConfig, "loop", true, config.loop)) {
    return {config, "invalid wav.loop option"};
  }
  if (!NodeHelpers::GetBool(isolate, jsConfig, "simulateTiming", true, config.simulateTiming)) {
    return {config, "invalid wav.simulateTiming option"};
  }
  return {config, ""};
}

std::tuple<BladeRFStreamConfig, std::string> convertBladeRFConfigFromJS(v8::Isolate* isolate, const v8::Local<v8::Object>& jsConfig) {
  BladeRFStreamConfig config;
  const v8::Local<v8::Context> ctx = isolate->GetCurrentContext();
  
  if (!NodeHelpers::GetNumber(isolate, jsConfig, "frequencyHz", config.frequencyHz, config.frequencyHz)) {
    return {config, "invalid bladerf.frequencyHz"};
  }
  if (!NodeHelpers::GetNumber(isolate, jsConfig, "sampleRateHz", config.sampleRateHz, config.sampleRateHz)) {
    return {config, "invalid bladerf.sampleRateHz"};
  }
  if (!NodeHelpers::GetNumber(isolate, jsConfig, "bandwidthHz", config.bandwidthHz, config.bandwidthHz)) {
    return {config, "invalid bladerf.bandwidthHz"};
  }
  if (!NodeHelpers::GetInt(isolate, jsConfig, "channel", config.channel, config.channel)) {
    return {config, "invalid bladerf.channel"};
  }
  if (!NodeHelpers::GetInt(isolate, jsConfig, "manualGainDb", 30, config.manualGainDb)) {
    return {config, "invalid bladerf.manualGainDb"};
  }
  
  // Handle gainMode string conversion
  std::string gainModeStr = "manual";
  v8::Local<v8::Value> gmVal;
  if (jsConfig->Get(ctx, v8::String::NewFromUtf8(isolate, "gainMode").ToLocalChecked()).ToLocal(&gmVal) && gmVal->IsString()) {
    v8::String::Utf8Value s(isolate, gmVal);
    gainModeStr = *s ? *s : "manual";
  }
  
  if (gainModeStr == "fast") {
    config.gainMode = BladeRFIQStream::GainMode::FAST;
  } else if (gainModeStr == "slow") {
    config.gainMode = BladeRFIQStream::GainMode::SLOW;
  } else if (gainModeStr == "default") {
    config.gainMode = BladeRFIQStream::GainMode::DEFAULT_;
  } else {
    config.gainMode = BladeRFIQStream::GainMode::MANUAL;
  }
  
  return {config, ""};
}

v8::Local<v8::Object> convertWAVConfigToJS(v8::Isolate* isolate, const WAVStreamConfig& config, const SampleRate actualSampleRate) {
  const v8::Local<v8::Context> ctx = isolate->GetCurrentContext();
  const v8::Local<v8::Object> payload = v8::Object::New(isolate);
  
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "type").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "wav").ToLocalChecked()).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sourceName").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "wav").ToLocalChecked()).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sampleRateHz").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(actualSampleRate))).Check();
  
  return payload;
}

v8::Local<v8::Object> convertBladeRFConfigToJS(
  v8::Isolate* isolate,
  const BladeRFStreamConfig& config,
  const BladeRFIQStream::ResolvedInfo& resolvedInfo
) {
  const v8::Local<v8::Context> ctx = isolate->GetCurrentContext();
  const v8::Local<v8::Object> payload = v8::Object::New(isolate);
  
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "type").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "bladerf").ToLocalChecked()).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sourceName").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "bladeRF").ToLocalChecked()).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "frequencyHz").ToLocalChecked(), v8::Number::New(isolate, resolvedInfo.frequencyHz)).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sampleRateHz").ToLocalChecked(), v8::Number::New(isolate, resolvedInfo.sampleRateHz)).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "bandwidthHz").ToLocalChecked(), v8::Number::New(isolate, resolvedInfo.bandwidthHz)).Check();
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "channel").ToLocalChecked(), v8::Number::New(isolate, resolvedInfo.channel)).Check();
  
  const char* gainModeStr =
    (resolvedInfo.gainMode == BladeRFIQStream::GainMode::MANUAL ? "manual" :
     resolvedInfo.gainMode == BladeRFIQStream::GainMode::FAST   ? "fast"   :
     resolvedInfo.gainMode == BladeRFIQStream::GainMode::SLOW   ? "slow"   : "default");
  
  payload->Set(ctx, v8::String::NewFromUtf8(isolate, "gainMode").ToLocalChecked(), v8::String::NewFromUtf8(isolate, gainModeStr).ToLocalChecked()).Check();
  
  if (resolvedInfo.gainMode == BladeRFIQStream::GainMode::MANUAL) {
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "manualGainDb").ToLocalChecked(), v8::Number::New(isolate, resolvedInfo.manualGainDb)).Check();
  }
  
  return payload;
}

}
